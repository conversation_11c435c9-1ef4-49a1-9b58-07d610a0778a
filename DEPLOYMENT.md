# Deployment Guide

This guide covers deploying the Home Services customer web application to production.

## Prerequisites

- Node.js 18+ installed
- Django REST Framework backend deployed and accessible
- Razorpay account for payment processing
- Domain name and SSL certificate (for PWA features)

## Environment Configuration

### Production Environment Variables

Create a `.env.local` file with the following variables:

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com/api

# Razorpay Configuration
NEXT_PUBLIC_RAZORPAY_KEY=rzp_live_your_live_key_here

# App Configuration
NEXT_PUBLIC_APP_NAME=Home Services
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### Security Considerations

1. **API URLs**: Ensure your backend API is served over HTTPS
2. **Razorpay Keys**: Use live keys for production, test keys for staging
3. **CORS**: Configure your Django backend to allow requests from your domain
4. **CSP**: Consider implementing Content Security Policy headers

## Deployment Options

### Option 1: Vercel (Recommended)

1. **Connect Repository**:
   ```bash
   # Push your code to GitHub/GitLab
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**:
   - Visit [vercel.com](https://vercel.com)
   - Import your repository
   - Configure environment variables
   - Deploy

3. **Configure Domain**:
   - Add your custom domain in Vercel dashboard
   - Update DNS records as instructed

### Option 2: Netlify

1. **Build Configuration**:
   Create `netlify.toml`:
   ```toml
   [build]
     command = "npm run build"
     publish = ".next"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. **Deploy**:
   - Connect repository to Netlify
   - Configure build settings
   - Set environment variables
   - Deploy

### Option 3: Self-Hosted (Docker)

1. **Create Dockerfile**:
   ```dockerfile
   FROM node:18-alpine AS deps
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production

   FROM node:18-alpine AS builder
   WORKDIR /app
   COPY . .
   COPY --from=deps /app/node_modules ./node_modules
   RUN npm run build

   FROM node:18-alpine AS runner
   WORKDIR /app
   ENV NODE_ENV production
   COPY --from=builder /app/public ./public
   COPY --from=builder /app/.next ./.next
   COPY --from=builder /app/node_modules ./node_modules
   COPY --from=builder /app/package.json ./package.json

   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Build and Run**:
   ```bash
   docker build -t home-services-customer .
   docker run -p 3000:3000 home-services-customer
   ```

## PWA Configuration

### HTTPS Requirement

PWA features require HTTPS in production. Ensure your deployment platform provides SSL certificates.

### Service Worker

The service worker is automatically generated by `next-pwa`. It will:
- Cache static assets
- Enable offline functionality
- Provide app-like experience

### App Installation

Users can install the app by:
1. Visiting the website on mobile
2. Tapping "Add to Home Screen" in browser menu
3. Following the installation prompt

## Performance Optimization

### Image Optimization

- Use Next.js Image component for automatic optimization
- Configure image domains in `next.config.js`
- Consider using a CDN for images

### Bundle Analysis

```bash
npm install --save-dev @next/bundle-analyzer
```

Add to `next.config.js`:
```javascript
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer(nextConfig);
```

Run analysis:
```bash
ANALYZE=true npm run build
```

### Caching Strategy

- Static assets: Long-term caching
- API responses: Short-term caching with revalidation
- User-specific data: No caching

## Monitoring and Analytics

### Error Tracking

Consider integrating:
- Sentry for error tracking
- LogRocket for session replay
- Google Analytics for user behavior

### Performance Monitoring

- Core Web Vitals monitoring
- Real User Monitoring (RUM)
- Synthetic monitoring for uptime

## Backup and Recovery

### Database Backups

Ensure your Django backend has proper backup procedures for:
- User data
- Order history
- Payment records

### Code Backups

- Use version control (Git)
- Tag releases for easy rollback
- Maintain staging environment

## Security Checklist

- [ ] HTTPS enabled
- [ ] Environment variables secured
- [ ] API endpoints protected
- [ ] Input validation implemented
- [ ] XSS protection enabled
- [ ] CSRF protection configured
- [ ] Rate limiting implemented
- [ ] Security headers configured

## Post-Deployment Testing

1. **Functionality Testing**:
   - User registration/login
   - Service browsing
   - Cart operations
   - Checkout process
   - Payment integration
   - Order management

2. **Performance Testing**:
   - Page load times
   - Mobile responsiveness
   - PWA installation
   - Offline functionality

3. **Security Testing**:
   - Authentication flows
   - Authorization checks
   - Input validation
   - Payment security

## Maintenance

### Regular Updates

- Keep dependencies updated
- Monitor security advisories
- Update Node.js version
- Review and update environment variables

### Monitoring

- Set up uptime monitoring
- Monitor error rates
- Track performance metrics
- Review user feedback

### Scaling

As your application grows, consider:
- CDN for static assets
- Database optimization
- Caching layers
- Load balancing
- Microservices architecture
