# Home Services - Customer Web Application

A complete Next.js customer-facing web application for booking professional home services. Built with modern technologies and integrated with Django REST Framework APIs.

## Features

### 🔐 Authentication & User Management
- Mobile/OTP and Email/Password login flows
- JWT token management with automatic refresh
- User profile management with mobile number verification
- Protected routes and authentication gates

### 🛒 Shopping & Cart Management
- Browse services by categories (hierarchical navigation)
- Advanced service search with filters
- Shopping cart with quantity management
- Coupon code application and discount management
- Login gate for cart actions

### 📦 Multi-Step Checkout Process
1. **Address Selection**: Manage delivery addresses
2. **Service Scheduling**: Select preferred date and time
3. **Payment**: Razorpay integration + Cash on Delivery

### 📱 Progressive Web App (PWA)
- Installable web app with manifest
- Service worker for caching and offline capabilities
- Responsive design for all devices
- App-like experience on mobile devices

### 📋 Order Management
- Complete order history and tracking
- Order details with service information
- Order cancellation with reason
- Invoice printing functionality

### 🎨 Modern UI/UX
- Responsive design with Tailwind CSS
- Loading states and error handling
- Toast notifications for user feedback
- Smooth animations and transitions

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **PWA**: next-pwa
- **Icons**: Lucide React
- **Payment**: Razorpay integration

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Django REST Framework backend running on `http://localhost:8000`

### Installation

1. **Clone and setup the project**:
   ```bash
   cd next_js_customer
   npm install
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.local.example .env.local
   ```

   Update the environment variables:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
   NEXT_PUBLIC_RAZORPAY_KEY=your_razorpay_key_here
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

### Quick Start Guide

1. **Browse Services**: Visit the homepage to see available service categories
2. **Search**: Use the search bar to find specific services
3. **Register/Login**: Create an account using mobile number + OTP or email/password
4. **Add to Cart**: Browse services and add them to your cart
5. **Checkout**: Complete the multi-step checkout process
6. **Track Orders**: View your order history and track service status

### Building for Production

```bash
npm run build
npm start
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── cart/              # Shopping cart
│   ├── categories/        # Category browsing
│   ├── checkout/          # Multi-step checkout
│   ├── orders/            # Order management
│   ├── profile/           # User profile
│   ├── search/            # Service search
│   └── services/          # Service details
├── components/            # Reusable components
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components
│   └── ui/                # UI components
├── contexts/              # React Context providers
├── lib/                   # Utility functions and API
└── types/                 # TypeScript type definitions
```

## API Integration

The application integrates with the Django REST Framework backend through:

- **Authentication**: JWT token management
- **Catalogue**: Categories and services
- **Cart**: Shopping cart operations
- **Orders**: Order creation and management
- **Payments**: Razorpay integration
- **User Management**: Profile and address management

## Key Features Implementation

### Authentication Flow
- Mobile registration with OTP verification
- JWT token storage and automatic refresh
- Protected routes with redirect handling

### Cart Management
- Anonymous and authenticated cart support
- Real-time cart updates
- Coupon application

### Checkout Process
- Address management with CRUD operations
- Service scheduling with date/time selection
- Payment integration with Razorpay and COD

### PWA Features
- Web app manifest for installability
- Service worker for caching
- Offline-first approach for better performance

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_BASE_URL` | Backend API base URL | `http://localhost:8000/api` |
| `NEXT_PUBLIC_RAZORPAY_KEY` | Razorpay public key | `rzp_test_key` |
| `NEXT_PUBLIC_APP_NAME` | Application name | `Home Services` |
| `NEXT_PUBLIC_APP_URL` | Application URL | `http://localhost:3000` |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## API Integration Details

The application seamlessly integrates with your Django REST Framework backend through well-defined API endpoints:

### Authentication Endpoints
- `POST /api/auth/register/mobile/` - Mobile registration
- `POST /api/auth/login/mobile/` - Mobile login with OTP
- `POST /api/auth/login/email/` - Email/password login
- `POST /api/auth/otp/send/` - Send OTP
- `POST /api/auth/otp/verify/` - Verify OTP
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile

### Catalogue Endpoints
- `GET /api/catalogue/categories/` - List categories
- `GET /api/catalogue/categories/{slug}/` - Category details
- `GET /api/catalogue/categories/{slug}/services/` - Category services
- `GET /api/catalogue/services/` - List services
- `GET /api/catalogue/services/{slug}/` - Service details
- `GET /api/catalogue/services/search/` - Search services

### Cart & Order Endpoints
- `GET /api/cart/` - Get cart
- `POST /api/cart/add/` - Add to cart
- `PUT /api/cart/items/{id}/update/` - Update cart item
- `DELETE /api/cart/items/{id}/remove/` - Remove cart item
- `POST /api/orders/` - Create order
- `GET /api/orders/` - List orders
- `GET /api/orders/{order_number}/` - Order details

### Payment Endpoints
- `POST /api/payments/initiate/` - Initiate payment
- `POST /api/payments/razorpay/callback/` - Razorpay callback

## Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions including:
- Environment configuration
- Platform-specific deployment guides (Vercel, Netlify, Docker)
- PWA setup and HTTPS requirements
- Performance optimization
- Security considerations

## Development Guidelines

### Code Structure
- Follow Next.js 14 App Router conventions
- Use TypeScript for type safety
- Implement proper error handling
- Follow responsive design principles

### State Management
- Use React Context for global state (auth, cart)
- Implement proper loading states
- Handle errors gracefully with user feedback

### Performance
- Optimize images with Next.js Image component
- Implement proper caching strategies
- Use code splitting and lazy loading
- Monitor Core Web Vitals

## Testing

```bash
# Run type checking
npm run build

# Run linting
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the [DEPLOYMENT.md](./DEPLOYMENT.md) for common issues
