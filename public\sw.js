if(!self.define){let e,s={};const n=(n,t)=>(n=new URL(n+".js",t).href,s[n]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()})).then((()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e})));self.define=(t,i)=>{const a=e||("document"in self?document.currentScript.src:"")||location.href;if(s[a])return;let c={};const r=e=>n(e,a),o={module:{uri:a},exports:c,require:r};s[a]=Promise.all(t.map((e=>o[e]||r(e)))).then((e=>(i(...e),c)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"dfc7b7de31b6b64ba40cff5d2247f0a1"},{url:"/_next/static/JGw9LifV8mDItECnxrpo9/_buildManifest.js",revision:"41a968fdf4a5a66f7c385d388f55a779"},{url:"/_next/static/JGw9LifV8mDItECnxrpo9/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/230-ef63612a8036af16.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/250-3b605e1d277afe19.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/274-bf27fa3737190dae.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/51-a094c8530f17aa56.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/761-9836992147f38f72.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/767-af96876c47dd2e03.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/938-ee36b187d0dcebb1.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/988-23ceec1f646ecf68.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/addresses/page-6992f6cf3e6a1ebe.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/auth/login/page-aa4466ee58814dc8.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/auth/register/page-2f1e72eae3ad80f0.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/cart/page-662f4b4384764a0d.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/categories/%5Bslug%5D/page-ec1b8a1fff9d2a4e.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/checkout/address/page-2029163efbb6790e.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/checkout/payment/page-b040a1225777bf70.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/checkout/schedule/page-f5fd98689aa947fb.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/contact/page-26cf9f95809756b4.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/error-ab4478baf8482ec8.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/layout-ab40066f2fb591cd.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/loading-9bc002f4a9be4cc9.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/not-found-3f33bd508b1d0061.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/orders/%5BorderNumber%5D/page-8997a918c73a8017.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/orders/page-45a69b86323e4aca.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/page-d2f517ac7612fdbc.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/profile/page-6496ae82aefee839.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/search/page-ec58bb29590d3f73.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/services/%5Bslug%5D/page-229bb870ecf5b26e.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/app/services/page-84601d6d83d90b6a.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/fd9d1056-bee54734699614b4.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/framework-c5181c9431ddc45b.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/main-4d19b7575b31082e.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/main-app-f93f729d78eedab7.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/pages/_app-98cb51ec6f9f135f.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/pages/_error-9157e57c362a0d0d.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js",revision:"837c0df77fd5009c9e46d446188ecfd0"},{url:"/_next/static/chunks/webpack-273f61469dce06d0.js",revision:"JGw9LifV8mDItECnxrpo9"},{url:"/_next/static/css/cba5362f9cf24460.css",revision:"cba5362f9cf24460"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/favicon.ico",revision:"e4d13187ccd69d9840e28588621cfa79"},{url:"/icons/README.md",revision:"c3f5f9239b745d0aa5711455ba5ad4f3"},{url:"/manifest.json",revision:"36aa0439ce6369286ee9692504c8c24b"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:n,state:t})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
