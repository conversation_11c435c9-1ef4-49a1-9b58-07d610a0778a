'use client';

import React, { useEffect, useState } from 'react';
import { Plus, MapPin, Home, Briefcase, Edit, Trash2, Star } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/components/ui/Toaster';
import { authApi } from '@/lib/api';
import { Address } from '@/types/api';

export default function AddressesPage() {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    address_type: 'HOME' as 'HOME' | 'WORK' | 'OTHER',
    street: '',
    city: '',
    state: '',
    zip_code: '',
    landmark: '',
    is_default: false,
  });

  const { showToast } = useToast();

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      const data = await authApi.getAddresses();
      setAddresses(data as Address[]);
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to load addresses', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      address_type: 'HOME',
      street: '',
      city: '',
      state: '',
      zip_code: '',
      landmark: '',
      is_default: false,
    });
    setEditingAddress(null);
    setShowAddForm(false);
  };

  const handleAddAddress = () => {
    resetForm();
    setShowAddForm(true);
  };

  const handleEditAddress = (address: Address) => {
    setFormData({
      address_type: address.address_type,
      street: address.street,
      city: address.city,
      state: address.state,
      zip_code: address.zip_code,
      landmark: address.landmark || '',
      is_default: address.is_default,
    });
    setEditingAddress(address);
    setShowAddForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.street || !formData.city || !formData.state || !formData.zip_code) {
      showToast({ type: 'error', title: 'Please fill all required fields' });
      return;
    }

    setIsSubmitting(true);
    try {
      if (editingAddress) {
        // Update existing address
        await authApi.updateAddress(editingAddress.id, formData);
        showToast({ type: 'success', title: 'Address updated successfully' });
      } else {
        // Create new address
        await authApi.createAddress(formData);
        showToast({ type: 'success', title: 'Address added successfully' });
      }
      
      await fetchAddresses();
      resetForm();
    } catch (error: any) {
      showToast({ 
        type: 'error', 
        title: editingAddress ? 'Failed to update address' : 'Failed to add address', 
        message: error.message 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAddress = async (addressId: number) => {
    if (!confirm('Are you sure you want to delete this address?')) {
      return;
    }

    try {
      await authApi.deleteAddress(addressId);
      setAddresses(addresses.filter(addr => addr.id !== addressId));
      showToast({ type: 'success', title: 'Address deleted successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to delete address', message: error.message });
    }
  };

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'HOME':
        return <Home className="h-5 w-5" />;
      case 'WORK':
        return <Briefcase className="h-5 w-5" />;
      default:
        return <MapPin className="h-5 w-5" />;
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <LoadingPage message="Loading addresses..." />
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">My Addresses</h1>
            <button
              onClick={handleAddAddress}
              className="btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </button>
          </div>

          {/* Address Form */}
          {showAddForm && (
            <div className="card p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {editingAddress ? 'Edit Address' : 'Add New Address'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Type
                    </label>
                    <select
                      value={formData.address_type}
                      onChange={(e) => setFormData({ ...formData, address_type: e.target.value as any })}
                      className="input"
                    >
                      <option value="HOME">Home</option>
                      <option value="WORK">Work</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    value={formData.street}
                    onChange={(e) => setFormData({ ...formData, street: e.target.value })}
                    placeholder="House/Flat No., Building Name, Street"
                    className="input"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City *
                    </label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State *
                    </label>
                    <input
                      type="text"
                      value={formData.state}
                      onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ZIP Code *
                    </label>
                    <input
                      type="text"
                      value={formData.zip_code}
                      onChange={(e) => setFormData({ ...formData, zip_code: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Landmark (Optional)
                  </label>
                  <input
                    type="text"
                    value={formData.landmark}
                    onChange={(e) => setFormData({ ...formData, landmark: e.target.value })}
                    placeholder="Nearby landmark for easy identification"
                    className="input"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_default"
                    checked={formData.is_default}
                    onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="is_default" className="text-sm text-gray-700">
                    Set as default address
                  </label>
                </div>

                <div className="flex space-x-4">
                  <LoadingButton
                    type="submit"
                    isLoading={isSubmitting}
                    className="btn-primary"
                  >
                    {editingAddress ? 'Update Address' : 'Add Address'}
                  </LoadingButton>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Addresses List */}
          {addresses.length > 0 ? (
            <div className="space-y-4">
              {addresses.map((address) => (
                <div key={address.id} className="card p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex items-center space-x-2">
                        {getAddressIcon(address.address_type)}
                        <span className="font-medium text-gray-900">
                          {address.address_type}
                        </span>
                        {address.is_default && (
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 mr-1" />
                            <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-xs font-medium">
                              Default
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditAddress(address)}
                        className="text-gray-400 hover:text-primary-600"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteAddress(address.id)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <p className="text-gray-700 mb-1">{address.street}</p>
                    <p className="text-gray-600 text-sm">
                      {address.city}, {address.state} {address.zip_code}
                    </p>
                    {address.landmark && (
                      <p className="text-gray-600 text-sm">Near {address.landmark}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <MapPin className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No addresses saved</h2>
              <p className="text-gray-600 mb-8">
                Add your first address to make checkout faster and easier.
              </p>
              <button
                onClick={handleAddAddress}
                className="btn-primary"
              >
                Add Your First Address
              </button>
            </div>
          )}
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
