'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Phone } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { authApi } from '@/lib/api';
import { LoginResponse } from '@/types/api';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    mobile_number: '',
    otp: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'register' | 'verify'>('register');
  const [otpTimer, setOtpTimer] = useState(0);

  const { login } = useAuth();
  const { showToast } = useToast();
  const router = useRouter();

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(60);
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.mobile_number) {
      showToast({ type: 'error', title: 'Please fill all fields' });
      return;
    }

    setIsLoading(true);
    try {
      // Register user
      await authApi.registerMobile({
        mobile_number: formData.mobile_number,
        name: formData.name,
        user_type: 'CUSTOMER',
      });

      // Send OTP
      await authApi.sendOTP(formData.mobile_number);
      
      setStep('verify');
      startOtpTimer();
      showToast({ type: 'success', title: 'Registration successful! Please verify your mobile number.' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Registration failed', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.otp) {
      showToast({ type: 'error', title: 'Please enter OTP' });
      return;
    }

    setIsLoading(true);
    try {
      // Verify OTP
      await authApi.verifyOTP(formData.mobile_number, formData.otp);
      
      // Login user
      const response = await authApi.loginMobile(formData.mobile_number, formData.otp);
      login(response as LoginResponse);
      
      showToast({ type: 'success', title: 'Account verified successfully!' });
      router.push('/');
    } catch (error: any) {
      showToast({ type: 'error', title: 'Verification failed', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    try {
      await authApi.sendOTP(formData.mobile_number);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP resent successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to resend OTP', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex items-center justify-center mb-6">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Home
        </Link>
        
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">HS</span>
          </div>
        </div>
        
        <h2 className="text-center text-3xl font-bold text-gray-900">
          {step === 'register' ? 'Create your account' : 'Verify your mobile number'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {step === 'register' ? (
            <>
              Already have an account?{' '}
              <Link href="/auth/login" className="font-medium text-primary-600 hover:text-primary-500">
                Sign in
              </Link>
            </>
          ) : (
            `We've sent an OTP to ${formData.mobile_number}`
          )}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {step === 'register' ? (
            <form onSubmit={handleRegister} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <div className="mt-1">
                  <input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter your full name"
                    className="input"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700">
                  Mobile Number
                </label>
                <div className="mt-1">
                  <input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    required
                    value={formData.mobile_number}
                    onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                    placeholder="+91 98765 43210"
                    className="input"
                  />
                </div>
              </div>

              <LoadingButton
                type="submit"
                isLoading={isLoading}
                className="w-full btn-primary"
              >
                Create Account
              </LoadingButton>
            </form>
          ) : (
            <form onSubmit={handleVerifyOTP} className="space-y-6">
              <div>
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                  Enter OTP
                </label>
                <div className="mt-1">
                  <input
                    id="otp"
                    name="otp"
                    type="text"
                    required
                    value={formData.otp}
                    onChange={(e) => setFormData({ ...formData, otp: e.target.value })}
                    placeholder="123456"
                    className="input"
                    maxLength={6}
                  />
                </div>
              </div>

              <div className="flex space-x-3">
                <LoadingButton
                  type="submit"
                  isLoading={isLoading}
                  className="flex-1 btn-primary"
                >
                  Verify & Continue
                </LoadingButton>
                
                {otpTimer === 0 ? (
                  <LoadingButton
                    type="button"
                    onClick={handleResendOTP}
                    isLoading={isLoading}
                    className="btn-outline"
                  >
                    Resend
                  </LoadingButton>
                ) : (
                  <button
                    type="button"
                    disabled
                    className="btn-outline opacity-50 cursor-not-allowed"
                  >
                    {otpTimer}s
                  </button>
                )}
              </div>

              <button
                type="button"
                onClick={() => setStep('register')}
                className="w-full text-sm text-gray-600 hover:text-gray-800"
              >
                Change mobile number
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
