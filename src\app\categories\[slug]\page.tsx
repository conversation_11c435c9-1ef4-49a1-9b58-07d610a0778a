'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, ChevronRight, ShoppingCart, Clock, Star } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { catalogueApi } from '@/lib/api';
import { Category, Service } from '@/types/api';

export default function CategoryPage() {
  const [category, setCategory] = useState<Category | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState<number | null>(null);

  const params = useParams();
  const router = useRouter();
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();

  const slug = params.slug as string;

  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        const [categoryData, servicesData] = await Promise.all([
          catalogueApi.getCategoryDetail(slug),
          catalogueApi.getCategoryServices(slug),
        ]);
        setCategory(categoryData as Category);
        setServices(servicesData as Service[]);
      } catch (error) {
        console.error('Failed to fetch category data:', error);
        showToast({ type: 'error', title: 'Failed to load category' });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategoryData();
  }, [slug, showToast]);

  const handleAddToCart = async (service: Service) => {
    if (!isAuthenticated) {
      router.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setAddingToCart(service.id);
    try {
      await addToCart(service);
      showToast({ type: 'success', title: 'Added to cart', message: service.title });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to add to cart', message: error.message });
    } finally {
      setAddingToCart(null);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <LoadingPage message="Loading category..." />
      </MainLayout>
    );
  }

  if (!category) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Category not found</h1>
          <Link href="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary-600">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 font-medium">{category.name}</span>
        </nav>

        {/* Category Header */}
        <div className="mb-12">
          <div className="flex items-center space-x-6">
            {category.image && (
              <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                <Image
                  src={category.image}
                  alt={category.name}
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">{category.name}</h1>
              <p className="text-lg text-gray-600 mb-4">{category.description}</p>
              <div className="text-sm text-gray-500">
                {category.services_count} services available
              </div>
            </div>
          </div>
        </div>

        {/* Subcategories */}
        {category.children && category.children.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by Category</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {category.children.map((subcategory) => (
                <Link
                  key={subcategory.id}
                  href={`/categories/${subcategory.slug}`}
                  className="card-hover p-4 text-center group"
                >
                  <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-primary-600">
                    {subcategory.name}
                  </h3>
                  <div className="flex items-center justify-center text-primary-600 group-hover:text-primary-700">
                    <span className="text-sm">View Services</span>
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Services */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Services</h2>
          {services.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {services.map((service) => (
                <div key={service.id} className="card-hover overflow-hidden">
                  {/* Service Image */}
                  <div className="h-48 bg-gray-200 relative">
                    {service.image ? (
                      <Image
                        src={service.image}
                        alt={service.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-primary-100 flex items-center justify-center">
                        <span className="text-primary-600 font-bold text-2xl">
                          {service.title.charAt(0)}
                        </span>
                      </div>
                    )}
                    {service.discount_percentage && service.discount_percentage > 0 && (
                      <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                        {service.discount_percentage}% OFF
                      </div>
                    )}
                  </div>

                  {/* Service Details */}
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {service.description}
                    </p>

                    {/* Service Info */}
                    <div className="flex items-center space-x-4 mb-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {service.time_to_complete}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1 text-yellow-400" />
                        4.8
                      </div>
                    </div>

                    {/* Pricing */}
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl font-bold text-gray-900">
                            ₹{service.current_price}
                          </span>
                          {service.discount_price && service.base_price !== service.current_price && (
                            <span className="text-lg text-gray-500 line-through">
                              ₹{service.base_price}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Link
                        href={`/services/${service.slug}`}
                        className="flex-1 btn-outline text-center"
                      >
                        View Details
                      </Link>
                      <LoadingButton
                        onClick={() => handleAddToCart(service)}
                        isLoading={addingToCart === service.id}
                        className="flex-1 btn-primary"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </LoadingButton>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600">No services available in this category.</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
