'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, MapPin, Home, Briefcase, Edit, Trash2 } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/components/ui/Toaster';
import { authApi } from '@/lib/api';
import { Address } from '@/types/api';

export default function CheckoutAddressPage() {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newAddress, setNewAddress] = useState({
    address_type: 'HOME' as 'HOME' | 'WORK' | 'OTHER',
    street: '',
    city: '',
    state: '',
    zip_code: '',
    landmark: '',
    is_default: false,
  });

  const router = useRouter();
  const { showToast } = useToast();

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      const data = await authApi.getAddresses();
      setAddresses(data as Address[]);
      // Auto-select default address
      const defaultAddress = (data as Address[]).find((addr: Address) => addr.is_default);
      if (defaultAddress) {
        setSelectedAddress(defaultAddress.id);
      }
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to load addresses', message: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newAddress.street || !newAddress.city || !newAddress.state || !newAddress.zip_code) {
      showToast({ type: 'error', title: 'Please fill all required fields' });
      return;
    }

    setIsSubmitting(true);
    try {
      const createdAddress = await authApi.createAddress(newAddress);
      setAddresses([...addresses, createdAddress]);
      setSelectedAddress(createdAddress.id);
      setShowAddForm(false);
      setNewAddress({
        address_type: 'HOME',
        street: '',
        city: '',
        state: '',
        zip_code: '',
        landmark: '',
        is_default: false,
      });
      showToast({ type: 'success', title: 'Address added successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to add address', message: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAddress = async (addressId: number) => {
    try {
      await authApi.deleteAddress(addressId);
      setAddresses(addresses.filter(addr => addr.id !== addressId));
      if (selectedAddress === addressId) {
        setSelectedAddress(null);
      }
      showToast({ type: 'success', title: 'Address deleted successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to delete address', message: error.message });
    }
  };

  const handleContinue = () => {
    if (!selectedAddress) {
      showToast({ type: 'error', title: 'Please select a delivery address' });
      return;
    }
    
    // Store selected address in session storage for next step
    const address = addresses.find(addr => addr.id === selectedAddress);
    if (address) {
      sessionStorage.setItem('selectedAddress', JSON.stringify(address));
      router.push('/checkout/schedule');
    }
  };

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'HOME':
        return <Home className="h-5 w-5" />;
      case 'WORK':
        return <Briefcase className="h-5 w-5" />;
      default:
        return <MapPin className="h-5 w-5" />;
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <LoadingPage message="Loading addresses..." />
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <span className="ml-2 text-sm font-medium text-primary-600">Address</span>
              </div>
              <div className="w-16 h-0.5 bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <span className="ml-2 text-sm text-gray-600">Schedule</span>
              </div>
              <div className="w-16 h-0.5 bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm text-gray-600">Payment</span>
              </div>
            </div>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-8">Select Delivery Address</h1>

          {/* Existing Addresses */}
          <div className="space-y-4 mb-8">
            {addresses.map((address) => (
              <div
                key={address.id}
                className={`card p-6 cursor-pointer transition-colors ${
                  selectedAddress === address.id
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedAddress(address.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <input
                      type="radio"
                      checked={selectedAddress === address.id}
                      onChange={() => setSelectedAddress(address.id)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {getAddressIcon(address.address_type)}
                        <span className="font-medium text-gray-900">
                          {address.address_type}
                        </span>
                        {address.is_default && (
                          <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-xs font-medium">
                            Default
                          </span>
                        )}
                      </div>
                      <p className="text-gray-700 mb-1">{address.street}</p>
                      <p className="text-gray-600 text-sm">
                        {address.city}, {address.state} {address.zip_code}
                      </p>
                      {address.landmark && (
                        <p className="text-gray-600 text-sm">Near {address.landmark}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-gray-600">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAddress(address.id);
                      }}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add New Address */}
          {!showAddForm ? (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full card p-6 border-2 border-dashed border-gray-300 hover:border-primary-500 hover:bg-primary-50 transition-colors flex items-center justify-center space-x-2 text-primary-600"
            >
              <Plus className="h-5 w-5" />
              <span className="font-medium">Add New Address</span>
            </button>
          ) : (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Address</h3>
              <form onSubmit={handleAddAddress} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address Type
                    </label>
                    <select
                      value={newAddress.address_type}
                      onChange={(e) => setNewAddress({ ...newAddress, address_type: e.target.value as any })}
                      className="input"
                    >
                      <option value="HOME">Home</option>
                      <option value="WORK">Work</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    value={newAddress.street}
                    onChange={(e) => setNewAddress({ ...newAddress, street: e.target.value })}
                    placeholder="House/Flat No., Building Name, Street"
                    className="input"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City *
                    </label>
                    <input
                      type="text"
                      value={newAddress.city}
                      onChange={(e) => setNewAddress({ ...newAddress, city: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State *
                    </label>
                    <input
                      type="text"
                      value={newAddress.state}
                      onChange={(e) => setNewAddress({ ...newAddress, state: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ZIP Code *
                    </label>
                    <input
                      type="text"
                      value={newAddress.zip_code}
                      onChange={(e) => setNewAddress({ ...newAddress, zip_code: e.target.value })}
                      className="input"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Landmark (Optional)
                  </label>
                  <input
                    type="text"
                    value={newAddress.landmark}
                    onChange={(e) => setNewAddress({ ...newAddress, landmark: e.target.value })}
                    placeholder="Nearby landmark for easy identification"
                    className="input"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_default"
                    checked={newAddress.is_default}
                    onChange={(e) => setNewAddress({ ...newAddress, is_default: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="is_default" className="text-sm text-gray-700">
                    Set as default address
                  </label>
                </div>

                <div className="flex space-x-4">
                  <LoadingButton
                    type="submit"
                    isLoading={isSubmitting}
                    className="btn-primary"
                  >
                    Add Address
                  </LoadingButton>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Continue Button */}
          <div className="mt-8 flex justify-end">
            <button
              onClick={handleContinue}
              disabled={!selectedAddress}
              className={`btn-primary ${!selectedAddress ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Continue to Schedule
            </button>
          </div>
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
