'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Clock, ArrowLeft } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useToast } from '@/components/ui/Toaster';

export default function CheckoutSchedulePage() {
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [availableTimes] = useState([
    '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'
  ]);

  const router = useRouter();
  const { showToast } = useToast();

  useEffect(() => {
    // Generate next 7 days as available dates
    const dates = [];
    const today = new Date();
    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date.toISOString().split('T')[0]);
    }
    setAvailableDates(dates);
  }, []);

  const handleContinue = () => {
    if (!selectedDate || !selectedTime) {
      showToast({ type: 'error', title: 'Please select both date and time' });
      return;
    }

    // Store schedule in session storage
    sessionStorage.setItem('selectedSchedule', JSON.stringify({
      date: selectedDate,
      time: selectedTime
    }));
    
    router.push('/checkout/payment');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Address</span>
              </div>
              <div className="w-16 h-0.5 bg-green-600"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <span className="ml-2 text-sm font-medium text-primary-600">Schedule</span>
              </div>
              <div className="w-16 h-0.5 bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm text-gray-600">Payment</span>
              </div>
            </div>
          </div>

          {/* Back Button */}
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-800 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Address
          </button>

          <h1 className="text-3xl font-bold text-gray-900 mb-8">Schedule Your Service</h1>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Date Selection */}
            <div className="card p-6">
              <div className="flex items-center mb-4">
                <Calendar className="h-5 w-5 text-primary-600 mr-2" />
                <h2 className="text-xl font-semibold text-gray-900">Select Date</h2>
              </div>
              <div className="space-y-2">
                {availableDates.map((date) => (
                  <button
                    key={date}
                    onClick={() => setSelectedDate(date)}
                    className={`w-full p-4 text-left rounded-lg border transition-colors ${
                      selectedDate === date
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">{formatDate(date)}</div>
                    <div className="text-sm text-gray-600">Available all day</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Time Selection */}
            <div className="card p-6">
              <div className="flex items-center mb-4">
                <Clock className="h-5 w-5 text-primary-600 mr-2" />
                <h2 className="text-xl font-semibold text-gray-900">Select Time</h2>
              </div>
              {!selectedDate ? (
                <div className="text-center py-8 text-gray-500">
                  Please select a date first
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3">
                  {availableTimes.map((time) => (
                    <button
                      key={time}
                      onClick={() => setSelectedTime(time)}
                      className={`p-3 text-center rounded-lg border transition-colors ${
                        selectedTime === time
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {formatTime(time)}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Selected Schedule Summary */}
          {selectedDate && selectedTime && (
            <div className="mt-8 card p-6 bg-primary-50 border-primary-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Selected Schedule</h3>
              <div className="flex items-center space-x-4 text-gray-700">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{formatDate(selectedDate)}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  <span>{formatTime(selectedTime)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Service Information */}
          <div className="mt-8 card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Important Information</h3>
            <ul className="space-y-2 text-gray-600">
              <li>• Our professional will arrive at your scheduled time</li>
              <li>• Please ensure someone is available at the service location</li>
              <li>• You can reschedule up to 2 hours before the appointment</li>
              <li>• Service duration may vary based on the scope of work</li>
              <li>• Payment can be made after service completion</li>
            </ul>
          </div>

          {/* Continue Button */}
          <div className="mt-8 flex justify-end">
            <button
              onClick={handleContinue}
              disabled={!selectedDate || !selectedTime}
              className={`btn-primary ${
                !selectedDate || !selectedTime ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              Continue to Payment
            </button>
          </div>
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
