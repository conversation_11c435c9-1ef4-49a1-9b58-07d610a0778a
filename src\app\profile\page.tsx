'use client';

import React, { useState } from 'react';
import { User, Phone, Mail, Edit2, Save, X } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { authApi } from '@/lib/api';

export default function ProfilePage() {
  const { user, updateUser } = useAuth();
  const { showToast } = useToast();
  
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    mobile_number: user?.mobile_number || '',
  });
  const [showMobileOTP, setShowMobileOTP] = useState(false);
  const [otp, setOtp] = useState('');
  const [otpTimer, setOtpTimer] = useState(0);

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(60);
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setFormData({
      name: user?.name || '',
      mobile_number: user?.mobile_number || '',
    });
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowMobileOTP(false);
    setOtp('');
    setFormData({
      name: user?.name || '',
      mobile_number: user?.mobile_number || '',
    });
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      showToast({ type: 'error', title: 'Name is required' });
      return;
    }

    // Check if mobile number changed
    const mobileChanged = formData.mobile_number !== user?.mobile_number;

    if (mobileChanged) {
      // Send OTP for mobile verification
      try {
        await authApi.sendOTP(formData.mobile_number);
        setShowMobileOTP(true);
        startOtpTimer();
        showToast({ type: 'success', title: 'OTP sent to new mobile number' });
      } catch (error: any) {
        showToast({ type: 'error', title: 'Failed to send OTP', message: error.message });
      }
    } else {
      // Update only name
      await updateProfile();
    }
  };

  const updateProfile = async () => {
    setIsUpdating(true);
    try {
      const updateData: any = { name: formData.name };
      
      // If mobile number changed and OTP is provided, include it
      if (formData.mobile_number !== user?.mobile_number && otp) {
        // First verify OTP
        await authApi.verifyOTP(formData.mobile_number, otp);
        updateData.mobile_number = formData.mobile_number;
      }

      await authApi.updateProfile(updateData);
      updateUser(updateData);
      
      setIsEditing(false);
      setShowMobileOTP(false);
      setOtp('');
      showToast({ type: 'success', title: 'Profile updated successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to update profile', message: error.message });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleVerifyAndUpdate = async () => {
    if (!otp.trim()) {
      showToast({ type: 'error', title: 'Please enter OTP' });
      return;
    }
    await updateProfile();
  };

  const handleResendOTP = async () => {
    try {
      await authApi.sendOTP(formData.mobile_number);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP resent successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to resend OTP', message: error.message });
    }
  };

  if (!user) {
    return (
      <MainLayout>
        <ProtectedRoute>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
            <p className="text-gray-600">Loading profile...</p>
          </div>
        </ProtectedRoute>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <ProtectedRoute>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">My Profile</h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Information */}
            <div className="lg:col-span-2">
              <div className="card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Personal Information</h2>
                  {!isEditing && (
                    <button
                      onClick={handleEdit}
                      className="btn-outline flex items-center"
                    >
                      <Edit2 className="h-4 w-4 mr-2" />
                      Edit
                    </button>
                  )}
                </div>

                {!showMobileOTP ? (
                  <div className="space-y-6">
                    {/* Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          className="input"
                          placeholder="Enter your full name"
                        />
                      ) : (
                        <div className="flex items-center space-x-3">
                          <User className="h-5 w-5 text-gray-400" />
                          <span className="text-gray-900">{user.name}</span>
                        </div>
                      )}
                    </div>

                    {/* Mobile Number */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Mobile Number
                      </label>
                      {isEditing ? (
                        <input
                          type="tel"
                          value={formData.mobile_number}
                          onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                          className="input"
                          placeholder="Enter your mobile number"
                        />
                      ) : (
                        <div className="flex items-center space-x-3">
                          <Phone className="h-5 w-5 text-gray-400" />
                          <span className="text-gray-900">{user.mobile_number}</span>
                          {user.is_verified && (
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                              Verified
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Email (if available) */}
                    {user.email && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address
                        </label>
                        <div className="flex items-center space-x-3">
                          <Mail className="h-5 w-5 text-gray-400" />
                          <span className="text-gray-900">{user.email}</span>
                        </div>
                      </div>
                    )}

                    {/* User Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Account Type
                      </label>
                      <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                        {user.user_type}
                      </span>
                    </div>

                    {/* Member Since */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Member Since
                      </label>
                      <span className="text-gray-900">
                        {new Date(user.date_joined).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    {isEditing && (
                      <div className="flex space-x-3 pt-4">
                        <LoadingButton
                          onClick={handleSave}
                          isLoading={isUpdating}
                          className="btn-primary flex items-center"
                        >
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </LoadingButton>
                        <button
                          onClick={handleCancel}
                          className="btn-secondary flex items-center"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Cancel
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  /* OTP Verification */
                  <div className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium text-blue-900 mb-2">Verify New Mobile Number</h3>
                      <p className="text-blue-700">
                        We've sent an OTP to {formData.mobile_number}. Please enter it below to verify your new mobile number.
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Enter OTP
                      </label>
                      <input
                        type="text"
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        placeholder="123456"
                        className="input"
                        maxLength={6}
                      />
                    </div>

                    <div className="flex space-x-3">
                      <LoadingButton
                        onClick={handleVerifyAndUpdate}
                        isLoading={isUpdating}
                        className="btn-primary"
                      >
                        Verify & Update
                      </LoadingButton>
                      
                      {otpTimer === 0 ? (
                        <button
                          onClick={handleResendOTP}
                          className="btn-outline"
                        >
                          Resend OTP
                        </button>
                      ) : (
                        <button
                          disabled
                          className="btn-outline opacity-50 cursor-not-allowed"
                        >
                          Resend in {otpTimer}s
                        </button>
                      )}
                      
                      <button
                        onClick={handleCancel}
                        className="btn-secondary"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <a
                    href="/orders"
                    className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="font-medium text-gray-900">My Orders</div>
                    <div className="text-sm text-gray-600">View your service bookings</div>
                  </a>
                  
                  <a
                    href="/addresses"
                    className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="font-medium text-gray-900">Manage Addresses</div>
                    <div className="text-sm text-gray-600">Add or edit delivery addresses</div>
                  </a>
                  
                  <a
                    href="/support"
                    className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="font-medium text-gray-900">Help & Support</div>
                    <div className="text-sm text-gray-600">Get help with your account</div>
                  </a>
                </div>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Security</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Mobile Verified</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      user.is_verified 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {user.is_verified ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Account Status</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                      Active
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    </MainLayout>
  );
}
